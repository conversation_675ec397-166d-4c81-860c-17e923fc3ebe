/**
 * 异步产品对比功能测试
 * 测试异步产品对比服务的核心功能
 */

const { compareProductsAsync } = require('../src/services/product/productCompareAsyncService');
const ProductComparisonV4Cache = require('../src/models/ProductComparisonV4Cache');
const notificationService = require('../src/services/notificationService');

// 模拟测试数据
const testProductNames = ['iPhone 15 Pro', 'Samsung Galaxy S24 Ultra'];
const testUserId = '507f1f77bcf86cd799439011'; // 模拟的ObjectId

/**
 * 测试异步产品对比功能
 */
async function testAsyncProductComparison() {
  console.log('🧪 开始测试异步产品对比功能...');
  
  try {
    // 测试1: 验证输入参数
    console.log('\n📋 测试1: 验证输入参数');
    
    // 测试产品数量不足
    try {
      const result1 = await compareProductsAsync(['iPhone 15'], testUserId);
      console.log('❌ 应该抛出错误：产品数量不足');
    } catch (error) {
      console.log('✅ 正确捕获错误：产品数量不足');
    }
    
    // 测试用户ID为空
    try {
      const result2 = await compareProductsAsync(testProductNames, null);
      console.log('❌ 应该抛出错误：用户ID为空');
    } catch (error) {
      console.log('✅ 正确捕获错误：用户ID为空');
    }
    
    // 测试2: 检查缓存逻辑
    console.log('\n📋 测试2: 检查缓存逻辑');
    
    // 清除可能存在的缓存
    const comparisonKey = ProductComparisonV4Cache.generateComparisonKey(testProductNames);
    await ProductComparisonV4Cache.deleteOne({ comparisonKey });
    console.log('🗑️ 清除测试缓存');
    
    // 第一次调用 - 应该返回异步状态
    const result3 = await compareProductsAsync(testProductNames, testUserId);
    
    if (result3.success && result3.data.isAsync) {
      console.log('✅ 第一次调用正确返回异步状态');
      console.log('📝 返回消息:', result3.data.message);
      console.log('⏱️ 预估时间:', result3.data.estimatedTime);
    } else {
      console.log('❌ 第一次调用应该返回异步状态');
    }
    
    // 测试3: 验证通知创建功能
    console.log('\n📋 测试3: 验证通知创建功能');
    
    try {
      // 模拟创建完成通知
      await notificationService.createNotification(
        testUserId,
        null,
        'product_comparison_completed',
        '测试通知：您的产品对比分析已完成',
        testUserId,
        'product_comparison',
        {
          productNames: testProductNames,
          completedAt: new Date()
        }
      );
      console.log('✅ 通知创建功能正常');
    } catch (error) {
      console.log('❌ 通知创建失败:', error.message);
    }
    
    console.log('\n🎉 异步产品对比功能测试完成');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

/**
 * 测试通知服务功能
 */
async function testNotificationService() {
  console.log('\n🧪 开始测试通知服务功能...');
  
  try {
    // 创建测试通知
    const notification = await notificationService.createNotification(
      testUserId,
      null,
      'product_comparison_completed',
      '测试通知：产品对比分析已完成',
      testUserId,
      'product_comparison',
      {
        productNames: testProductNames,
        testMode: true
      }
    );
    
    console.log('✅ 通知创建成功');
    console.log('📝 通知ID:', notification._id);
    console.log('📝 通知内容:', notification.content);
    
    // 清理测试数据
    await notification.deleteOne();
    console.log('🗑️ 清理测试通知');
    
  } catch (error) {
    console.error('❌ 通知服务测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  // 需要连接数据库才能运行测试
  const mongoose = require('mongoose');
  const connectDB = require('../src/config/database');
  
  async function runTests() {
    try {
      // 连接数据库
      await connectDB();
      console.log('📊 数据库连接成功');
      
      // 运行测试
      await testAsyncProductComparison();
      await testNotificationService();
      
      console.log('\n✅ 所有测试完成');
      
    } catch (error) {
      console.error('❌ 测试运行失败:', error);
    } finally {
      // 关闭数据库连接
      await mongoose.connection.close();
      console.log('📊 数据库连接已关闭');
    }
  }
  
  runTests();
}

module.exports = {
  testAsyncProductComparison,
  testNotificationService
};
